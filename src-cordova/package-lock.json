{"name": "beifeng.scenic.system", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "beifeng.scenic.system", "version": "1.0.0", "license": "Apache-2.0", "dependencies": {"cordova-plugin-appavailability": "0.4.2", "cordova-plugin-camera": "^7.0.0", "cordova-plugin-file-opener2": "^4.0.0", "cordova-plugin-file-transfer": "^2.0.0", "cordova-plugin-geolocation": "^5.0.0", "cordova-plugin-startapp": "^0.1.6"}, "devDependencies": {"community-cordova-plugin-nfc": "^1.4.0", "cordova-android": "^12.0.1", "cordova-plugin-android-permissions": "^1.0.2", "cordova-plugin-background-mode": "^0.7.3", "cordova-plugin-badge-fix": "^0.8.10", "cordova-plugin-device": "^2.1.0", "cordova-plugin-file": "^8.0.1", "cordova-plugin-ionic-webview": "^5.0.0", "cordova-plugin-local-notification-12": "^0.1.4", "cordova-plugin-splashscreen": "github:apache/cordova-plugin-splashscreen", "cordova-plugin-vibration": "^3.1.1"}}, "node_modules/@netflix/nerror": {"version": "1.1.3", "resolved": "https://registry.npmmirror.com/@netflix/nerror/-/nerror-1.1.3.tgz", "integrity": "sha512-b+MGNyP9/LXkapreJzNUzcvuzZslj/RGgdVVJ16P2wSlYatfLycPObImqVJSmNAdyeShvNeM/pl3sVZsObFueg==", "dev": true, "license": "MIT", "dependencies": {"assert-plus": "^1.0.0", "extsprintf": "^1.4.0", "lodash": "^4.17.15"}}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "resolved": "https://registry.npmmirror.com/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz", "integrity": "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "resolved": "https://registry.npmmirror.com/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "integrity": "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "resolved": "https://registry.npmmirror.com/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "integrity": "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@xmldom/xmldom": {"version": "0.8.10", "resolved": "https://registry.npmmirror.com/@xmldom/xmldom/-/xmldom-0.8.10.tgz", "integrity": "sha512-2WALfTl4xo2SkGCYRt6rDTFfk9R1czmBvUQy12gK2KuRKIpWEhcbbzy8EZXtz/jkRqHX8bFEc6FC1HjX4TUWYw==", "dev": true, "license": "MIT", "engines": {"node": ">=10.0.0"}}, "node_modules/abbrev": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/abbrev/-/abbrev-2.0.0.tgz", "integrity": "sha512-6/mh1E2u2YgEsCHdY0Yx5oW+61gZU+1vXaoiHHrpKeuRNNgFvS+/jrwHiQhB5apAf5oB7UB7E19ol2R2LKH8hQ==", "dev": true, "license": "ISC", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/android-versions": {"version": "1.9.0", "resolved": "https://registry.npmmirror.com/android-versions/-/android-versions-1.9.0.tgz", "integrity": "sha512-13O2B6PQMEM4ej9n13ePRQeckrCoKbZrvuzlLvK+9s2QmncpHDbYzZxhgapN32sJNoifN6VAHexLnd/6CYrs7Q==", "dev": true, "license": "MIT", "dependencies": {"semver": "^7.5.2"}}, "node_modules/ansi": {"version": "0.3.1", "resolved": "https://registry.npmmirror.com/ansi/-/ansi-0.3.1.tgz", "integrity": "sha512-iFY7JCgHbepc0b82yLaw4IMortylNb6wG4kL+4R0C3iv6i+RHGHux/yUX5BTiRvSX/shMnngjR1YyNMnXEFh5A==", "dev": true, "license": "MIT"}, "node_modules/assert-plus": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw==", "dev": true, "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/base64-js": {"version": "1.5.1", "resolved": "https://registry.npmmirror.com/base64-js/-/base64-js-1.5.1.tgz", "integrity": "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/big-integer": {"version": "1.6.52", "resolved": "https://registry.npmmirror.com/big-integer/-/big-integer-1.6.52.tgz", "integrity": "sha512-QxD8cf2eVqJOOz63z6JIN9BzvVs/dlySa5HGSBH5xtR8dPteIRQnBxxKqkNTiT6jbDTF6jAfrd4oMcND9RGbQg==", "dev": true, "license": "Unlicense", "engines": {"node": ">=0.6"}}, "node_modules/bplist-parser": {"version": "0.3.2", "resolved": "https://registry.npmmirror.com/bplist-parser/-/bplist-parser-0.3.2.tgz", "integrity": "sha512-apC2+fspHGI3mMKj+dGevkGo/tCqVB8jMb6i+OX+E29p0Iposz07fABkRIfVUPNd5A5VbuOz1bZbnmkKLYF+wQ==", "dev": true, "license": "MIT", "dependencies": {"big-integer": "1.6.x"}, "engines": {"node": ">= 5.10.0"}}, "node_modules/braces": {"version": "3.0.3", "resolved": "https://registry.npmmirror.com/braces/-/braces-3.0.3.tgz", "integrity": "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==", "dev": true, "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/community-cordova-plugin-nfc": {"version": "1.4.0", "resolved": "https://registry.npmmirror.com/community-cordova-plugin-nfc/-/community-cordova-plugin-nfc-1.4.0.tgz", "integrity": "sha512-VovCTcSSKpgEN41Z5Cp1KjOC0nF3pqmQ1budIWG0dCxGwcalSacijGlot7pUlbxdPcUzHhHGIn9Jy0eCfLGuBg==", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/EYALIN"}}, "node_modules/cordova-android": {"version": "12.0.1", "resolved": "https://registry.npmmirror.com/cordova-android/-/cordova-android-12.0.1.tgz", "integrity": "sha512-6fXWoYb/X5AJlluhBg5RvOaX6iRxsvNH7yEOiXzUmLIflf5Ho7LtYCSKZaWMHh3f5cJe/sPCBKmFFBS+EWepVg==", "dev": true, "license": "Apache-2.0", "dependencies": {"android-versions": "^1.8.1", "cordova-common": "^5.0.0", "dedent": "^1.0.1", "execa": "^5.1.1", "fast-glob": "^3.2.12", "fs-extra": "^11.1.1", "is-path-inside": "^3.0.3", "nopt": "^7.1.0", "properties-parser": "^0.3.1", "semver": "^7.3.8", "string-argv": "^0.3.1", "untildify": "^4.0.0", "which": "^3.0.0"}, "engines": {"node": ">=16.13.0"}}, "node_modules/cordova-common": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/cordova-common/-/cordova-common-5.0.1.tgz", "integrity": "sha512-OA2NQ6wvhNz4GytPYwTdlA9xfG7Yf7ufkj4u97m3rUfoL/AECwwj0GVT2CYpk/0Fk6HyuHA3QYCxfDPYsKzI1A==", "dev": true, "license": "Apache-2.0", "dependencies": {"@netflix/nerror": "^1.1.3", "ansi": "^0.3.1", "bplist-parser": "^0.3.2", "cross-spawn": "^7.0.6", "elementtree": "^0.1.7", "endent": "^2.1.0", "fast-glob": "^3.3.3", "lodash.zip": "^4.2.0", "plist": "^3.1.0", "q": "^1.5.1", "read-chunk": "^3.2.0", "strip-bom": "^4.0.0"}, "engines": {"node": ">=16.0.0"}}, "node_modules/cordova-plugin-android-permissions": {"version": "1.1.5", "resolved": "https://registry.npmmirror.com/cordova-plugin-android-permissions/-/cordova-plugin-android-permissions-1.1.5.tgz", "integrity": "sha512-oTTV9cCMBqXTCmU+nYRebsP2IQfrtdvl2vYXHjoJgv5NHCIDgY4KFg6kJTcwXQOiymeGXuw0+MTvJJOueAdleA==", "dev": true, "engines": [{"name": "<PERSON><PERSON>", "version": ">=5.0.0"}]}, "node_modules/cordova-plugin-appavailability": {"version": "0.4.2", "resolved": "https://registry.npmmirror.com/cordova-plugin-appavailability/-/cordova-plugin-appavailability-0.4.2.tgz", "integrity": "sha512-kN8yIfCtMH5dCR8LyJMKyosI/itnPCEXjoZwhRXWYV6tFNRueqcBSNklvwhENEwZaeZjoTamVNTy35y8p/TRDQ==", "engines": [{"name": "<PERSON><PERSON>", "version": ">=3.0.0"}]}, "node_modules/cordova-plugin-background-mode": {"version": "0.7.3", "resolved": "https://registry.npmmirror.com/cordova-plugin-background-mode/-/cordova-plugin-background-mode-0.7.3.tgz", "integrity": "sha512-LsU1v7EgTUROaks+tcQ8TnMzVUcU/TwjDVwj2O/4e4aI2q2ldLGsiZorqKqfqvwh2HoIssmY73OwJk91hQi62w==", "dev": true, "engines": [{"name": "<PERSON><PERSON>", "version": ">=3.0.0"}, {"name": "android-sdk", "version": ">=16"}]}, "node_modules/cordova-plugin-badge-fix": {"version": "0.8.10", "resolved": "https://registry.npmmirror.com/cordova-plugin-badge-fix/-/cordova-plugin-badge-fix-0.8.10.tgz", "integrity": "sha512-4yvfe5Qil9Bz7OTSnVNVkQV5Mru0wSAgfAhBE/myo9aPLT7yjs3sn963Shg4MCDPlVCganS0spJ384fsyYB6lA==", "dev": true, "engines": [{"name": "<PERSON><PERSON>", "version": ">=6.0.0"}, {"name": "apple-ios", "version": ">=10.0.0"}, {"name": "cordova-android", "version": ">=4"}, {"name": "cordova-plugman", "version": ">=4.2.0"}]}, "node_modules/cordova-plugin-camera": {"version": "7.0.0", "resolved": "https://registry.npmmirror.com/cordova-plugin-camera/-/cordova-plugin-camera-7.0.0.tgz", "integrity": "sha512-OVQWZTBb18Y6e5c+bbXt3E4Z1yGnYqaywh2h5vVr/+nxMcdMIE+lm527bRK5vLN/RUqhGYP/Z+5n+O7Fk7fVNw==", "engines": {"cordovaDependencies": {"3.0.0": {"cordova-android": ">=6.3.0"}, "4.1.0": {"cordova": ">=7.1.0", "cordova-android": ">=6.3.0"}, "5.0.0": {"cordova": ">=9.0.0", "cordova-android": ">=9.0.0", "cordova-ios": ">=5.1.0"}, "5.0.4-dev": {"cordova": ">=9.0.0", "cordova-android": "<10.0.0", "cordova-ios": ">=5.1.0"}, "6.0.0": {"cordova": ">=9.0.0", "cordova-android": ">=10.0.0", "cordova-ios": ">=5.1.0"}, "7.0.0": {"cordova": ">=9.0.0", "cordova-android": ">=12.0.0", "cordova-ios": ">=5.1.0"}, "8.0.0": {"cordova": ">100"}}}}, "node_modules/cordova-plugin-device": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/cordova-plugin-device/-/cordova-plugin-device-2.1.0.tgz", "integrity": "sha512-FU0Lw1jZpuKOgG4v80LrfMAOIMCGfAVPumn7AwaX9S1iU/X3OPZUyoKUgP09q4bxL35IeNPkqNWVKYduAXZ1sg==", "dev": true, "engines": {"cordovaDependencies": {"3.0.0": {"cordova": ">100", "cordova-electron": ">=3.0.0"}}}}, "node_modules/cordova-plugin-file": {"version": "8.0.1", "resolved": "https://registry.npmmirror.com/cordova-plugin-file/-/cordova-plugin-file-8.0.1.tgz", "integrity": "sha512-LgFLNQN58xguoJkNc8eGBmg/Vuaah9lY3Nye27OAfWCKalXPRjExIg5r8L3qlfiJxzmzupjrF0M4KdU2Lovm3Q==", "dev": true, "engines": {"cordovaDependencies": {"5.0.0": {"cordova-android": ">=6.3.0"}, "7.0.0": {"cordova-android": ">=10.0.0"}, "8.0.0": {"cordova-android": ">=12.0.0"}, "9.0.0": {"cordova": ">100"}}}}, "node_modules/cordova-plugin-file-opener2": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/cordova-plugin-file-opener2/-/cordova-plugin-file-opener2-4.0.0.tgz", "integrity": "sha512-+O+MMldI2k5Kjoa62KXYxnZQIGE4k5VfoCmmOUmaV5XM6U6euiiKMfCkuMrOrdAFb1C5Jsx+SuBHbWx1NEB5lw==", "engines": {"cordovaDependencies": {"2.0.0": {"cordova": ">=6.0.0"}, "3.0.0": {"cordova": ">=7.0.0"}, "4.0.0": {"cordova-android": ">=10.0.0"}}}}, "node_modules/cordova-plugin-file-transfer": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/cordova-plugin-file-transfer/-/cordova-plugin-file-transfer-2.0.0.tgz", "integrity": "sha512-PPFM4+Gf5TQ1ttln2CxdrxH9JOcHBAuho2NQzLuCtXsb9X8Ivypz3cjpSgrAlWHwnk6OPvwo9DJ4GU2FXkGsrQ==", "engines": {"cordovaDependencies": {"2.0.0": {"cordova-android": ">=12.0.0"}, "3.0.0": {"cordova": ">100"}}}}, "node_modules/cordova-plugin-geolocation": {"version": "5.0.0", "resolved": "https://registry.npmmirror.com/cordova-plugin-geolocation/-/cordova-plugin-geolocation-5.0.0.tgz", "integrity": "sha512-eIk96pF59y3AK+NHeyA0si9eFhZElgOcFoGoTU9LDlnEYpfBN+V4ojRpKq3NMLCGTozew22/i9najFu9Kvb40g==", "engines": {"cordovaDependencies": {"3.0.0": {"cordova-android": ">=6.3.0"}, "6.0.0": {"cordova": ">100"}}}}, "node_modules/cordova-plugin-ionic-webview": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/cordova-plugin-ionic-webview/-/cordova-plugin-ionic-webview-5.0.1.tgz", "integrity": "sha512-dF3x7ee8s7ITPImz8WG5HFMnzMW1LaCve+Pdm5WzonKxcwkmMsPrW2zoWuIFBTWjXvFyNoxprzRn6aq/WotvKg==", "dev": true, "engines": {"cordovaDependencies": {"2.0.0": {"cordova-android": ">=6.4.0", "cordova-ios": ">=4.0.0-dev"}, "3.1.0": {"cordova": ">=7.1.0", "cordova-android": ">=6.4.0", "cordova-ios": ">=4.0.0-dev"}}}}, "node_modules/cordova-plugin-local-notification-12": {"version": "0.1.4", "resolved": "https://registry.npmmirror.com/cordova-plugin-local-notification-12/-/cordova-plugin-local-notification-12-0.1.4.tgz", "integrity": "sha512-b6HOrJUOTOzg3gPrp5puw4Rq3O2cqiJsnBiRfUOfIJsvvhgPs8V5DqmiR1mf+SoHQCXpNdhTAh6SX1hcIBsBQw==", "dev": true, "engines": [{"name": "<PERSON><PERSON>", "version": ">=3.6.0"}, {"name": "cordova-android", "version": ">=6.0.0"}, {"name": "cordova-windows", "version": ">=4.2.0"}, {"name": "android-sdk", "version": ">=26"}, {"name": "apple-ios", "version": ">=10.0.0"}]}, "node_modules/cordova-plugin-splashscreen": {"version": "7.0.0-dev", "resolved": "git+ssh://**************/apache/cordova-plugin-splashscreen.git#586b988371fc57919288caacf7e1486ac44d19ca", "dev": true, "license": "Apache-2.0", "engines": {"cordovaDependencies": {"<6.0.0": {"cordova-ios": "<6.0.0"}, "<7.0.0": {"cordova-android": ">=3.6.0 <11.0.0", "cordova-windows": ">=4.4.0"}, ">=4.0.0": {"cordova-android": ">=3.6.0", "cordova-windows": ">=4.4.0"}, "2.0.0": {"cordova-android": ">=3.6.0"}, "6.0.2": {"cordova-android": ">=3.6.0 <11.0.0", "cordova-windows": ">=4.4.0"}, "8.0.0": {"cordova": ">100"}}}}, "node_modules/cordova-plugin-startapp": {"version": "0.1.6", "resolved": "https://registry.npmmirror.com/cordova-plugin-startapp/-/cordova-plugin-startapp-0.1.6.tgz", "integrity": "sha512-jVFvsfG3rA12D97e43P8GOO0jQmykebPAyV/eW+cZV9UNg5EGtuTKporIKxQ12rVMDRuRPq6LTNKp2PAlFsgjg=="}, "node_modules/cordova-plugin-vibration": {"version": "3.1.1", "resolved": "https://registry.npmmirror.com/cordova-plugin-vibration/-/cordova-plugin-vibration-3.1.1.tgz", "integrity": "sha512-qgv67Rueo4Pydfant3TwnXeFiN9dl+6lKMM6h5jYg9XewiGAGOr8vfWsTvQssC3m3xMKGS1ap3xPNH+BzZ4RMA==", "dev": true, "engines": {"cordovaDependencies": {"4.0.0": {"cordova": ">100"}}}}, "node_modules/cross-spawn": {"version": "7.0.6", "resolved": "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.6.tgz", "integrity": "sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/cross-spawn/node_modules/which": {"version": "2.0.2", "resolved": "https://registry.npmmirror.com/which/-/which-2.0.2.tgz", "integrity": "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/dedent": {"version": "1.6.0", "resolved": "https://registry.npmmirror.com/dedent/-/dedent-1.6.0.tgz", "integrity": "sha512-F1Z+5UCFpmQUzJa11agbyPVMbpgT/qA3/SKyJ1jyBgm7dUcUEa8v9JwDkerSQXfakBwFljIxhOJqGkjUwZ9FSA==", "dev": true, "license": "MIT", "peerDependencies": {"babel-plugin-macros": "^3.1.0"}, "peerDependenciesMeta": {"babel-plugin-macros": {"optional": true}}}, "node_modules/elementtree": {"version": "0.1.7", "resolved": "https://registry.npmmirror.com/elementtree/-/elementtree-0.1.7.tgz", "integrity": "sha512-wkgGT6kugeQk/P6VZ/f4T+4HB41BVgNBq5CDIZVbQ02nvTVqAiVTbskxxu3eA/X96lMlfYOwnLQpN2v5E1zDEg==", "dev": true, "license": "Apache-2.0", "dependencies": {"sax": "1.1.4"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/endent": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/endent/-/endent-2.1.0.tgz", "integrity": "sha512-r8VyPX7XL8U01Xgnb1CjZ3XV+z90cXIJ9JPE/R9SEC9vpw2P6CfsRPJmp20DppC5N7ZAMCmjYkJIa744Iyg96w==", "dev": true, "license": "MIT", "dependencies": {"dedent": "^0.7.0", "fast-json-parse": "^1.0.3", "objectorarray": "^1.0.5"}}, "node_modules/endent/node_modules/dedent": {"version": "0.7.0", "resolved": "https://registry.npmmirror.com/dedent/-/dedent-0.7.0.tgz", "integrity": "sha512-Q6fKUPqnAHAyhiUgFU7BUzLiv0kd8saH9al7tnu5Q/okj6dnupxyTgFIBjVzJATdfIAm9NAsvXNzjaKa+bxVyA==", "dev": true, "license": "MIT"}, "node_modules/execa": {"version": "5.1.1", "resolved": "https://registry.npmmirror.com/execa/-/execa-5.1.1.tgz", "integrity": "sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==", "dev": true, "license": "MIT", "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/extsprintf": {"version": "1.4.1", "resolved": "https://registry.npmmirror.com/extsprintf/-/extsprintf-1.4.1.tgz", "integrity": "sha512-Wrk35e8ydCKDj/ArClo1VrPVmN8zph5V4AtHwIuHhvMXsKf73UT3BOD+azBIW+3wOJ4FhEH7zyaJCFvChjYvMA==", "dev": true, "engines": ["node >=0.6.0"], "license": "MIT"}, "node_modules/fast-glob": {"version": "3.3.3", "resolved": "https://registry.npmmirror.com/fast-glob/-/fast-glob-3.3.3.tgz", "integrity": "sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.8"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fast-json-parse": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/fast-json-parse/-/fast-json-parse-1.0.3.tgz", "integrity": "sha512-FRWsaZRWEJ1ESVNbDWmsAlqDk96gPQezzLghafp5J4GUKjbCz3OkAHuZs5TuPEtkbVQERysLp9xv6c24fBm8Aw==", "dev": true, "license": "MIT"}, "node_modules/fastq": {"version": "1.19.1", "resolved": "https://registry.npmmirror.com/fastq/-/fastq-1.19.1.tgz", "integrity": "sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==", "dev": true, "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/fill-range": {"version": "7.1.1", "resolved": "https://registry.npmmirror.com/fill-range/-/fill-range-7.1.1.tgz", "integrity": "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==", "dev": true, "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/fs-extra": {"version": "11.3.0", "resolved": "https://registry.npmmirror.com/fs-extra/-/fs-extra-11.3.0.tgz", "integrity": "sha512-Z4XaCL6dUDHfP/jT25jJKMmtxvuwbkrD1vNSMFlo9lNLY2c5FHYSQgHPRZUjAB26TpDEoW9HCOgplrdbaPV/ew==", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=14.14"}}, "node_modules/get-stream": {"version": "6.0.1", "resolved": "https://registry.npmmirror.com/get-stream/-/get-stream-6.0.1.tgz", "integrity": "sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/glob-parent": {"version": "5.1.2", "resolved": "https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz", "integrity": "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/graceful-fs": {"version": "4.2.11", "resolved": "https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.11.tgz", "integrity": "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==", "dev": true, "license": "ISC"}, "node_modules/human-signals": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/human-signals/-/human-signals-2.1.0.tgz", "integrity": "sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=10.17.0"}}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-glob": {"version": "4.0.3", "resolved": "https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz", "integrity": "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==", "dev": true, "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-number": {"version": "7.0.0", "resolved": "https://registry.npmmirror.com/is-number/-/is-number-7.0.0.tgz", "integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-path-inside": {"version": "3.0.3", "resolved": "https://registry.npmmirror.com/is-path-inside/-/is-path-inside-3.0.3.tgz", "integrity": "sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-stream": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/is-stream/-/is-stream-2.0.1.tgz", "integrity": "sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/isexe": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/isexe/-/isexe-2.0.0.tgz", "integrity": "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==", "dev": true, "license": "ISC"}, "node_modules/jsonfile": {"version": "6.1.0", "resolved": "https://registry.npmmirror.com/jsonfile/-/jsonfile-6.1.0.tgz", "integrity": "sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/lodash": {"version": "4.17.21", "resolved": "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz", "integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==", "dev": true, "license": "MIT"}, "node_modules/lodash.zip": {"version": "4.2.0", "resolved": "https://registry.npmmirror.com/lodash.zip/-/lodash.zip-4.2.0.tgz", "integrity": "sha512-C7IOaBBK/0gMORRBd8OETNx3kmOkgIWIPvyDpZSCTwUrpYmgZwJkjZeOD8ww4xbOUOs4/attY+pciKvadNfFbg==", "dev": true, "license": "MIT"}, "node_modules/merge-stream": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/merge-stream/-/merge-stream-2.0.0.tgz", "integrity": "sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==", "dev": true, "license": "MIT"}, "node_modules/merge2": {"version": "1.4.1", "resolved": "https://registry.npmmirror.com/merge2/-/merge2-1.4.1.tgz", "integrity": "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/micromatch": {"version": "4.0.8", "resolved": "https://registry.npmmirror.com/micromatch/-/micromatch-4.0.8.tgz", "integrity": "sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==", "dev": true, "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mimic-fn": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/mimic-fn/-/mimic-fn-2.1.0.tgz", "integrity": "sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/nopt": {"version": "7.2.1", "resolved": "https://registry.npmmirror.com/nopt/-/nopt-7.2.1.tgz", "integrity": "sha512-taM24ViiimT/XntxbPyJQzCG+p4EKOpgD3mxFwW38mGjVUrfERQOeY4EDHjdnptttfHuHQXFx+lTP08Q+mLa/w==", "dev": true, "license": "ISC", "dependencies": {"abbrev": "^2.0.0"}, "bin": {"nopt": "bin/nopt.js"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/npm-run-path": {"version": "4.0.1", "resolved": "https://registry.npmmirror.com/npm-run-path/-/npm-run-path-4.0.1.tgz", "integrity": "sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/objectorarray": {"version": "1.0.5", "resolved": "https://registry.npmmirror.com/objectorarray/-/objectorarray-1.0.5.tgz", "integrity": "sha512-eJJDYkhJFFbBBAxeh8xW+weHlkI28n2ZdQV/J/DNfWfSKlGEf2xcfAbZTv3riEXHAhL9SVOTs2pRmXiSTf78xg==", "dev": true, "license": "ISC"}, "node_modules/onetime": {"version": "5.1.2", "resolved": "https://registry.npmmirror.com/onetime/-/onetime-5.1.2.tgz", "integrity": "sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==", "dev": true, "license": "MIT", "dependencies": {"mimic-fn": "^2.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-finally": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/p-finally/-/p-finally-1.0.0.tgz", "integrity": "sha512-LICb2p9CB7FS+0eR1oqWnHhp0FljGLZCWBE9aix0Uye9W8LTQPwMTYVGWQWIw9RdQiDg4+epXQODwIYJtSJaow==", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/p-try": {"version": "2.2.0", "resolved": "https://registry.npmmirror.com/p-try/-/p-try-2.2.0.tgz", "integrity": "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/path-key": {"version": "3.1.1", "resolved": "https://registry.npmmirror.com/path-key/-/path-key-3.1.1.tgz", "integrity": "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/picomatch": {"version": "2.3.1", "resolved": "https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==", "dev": true, "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pify": {"version": "4.0.1", "resolved": "https://registry.npmmirror.com/pify/-/pify-4.0.1.tgz", "integrity": "sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/plist": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/plist/-/plist-3.1.0.tgz", "integrity": "sha512-uysumyrvkUX0rX/dEVqt8gC3sTBzd4zoWfLeS29nb53imdaXVvLINYXTI2GNqzaMuvacNx4uJQ8+b3zXR0pkgQ==", "dev": true, "license": "MIT", "dependencies": {"@xmldom/xmldom": "^0.8.8", "base64-js": "^1.5.1", "xmlbuilder": "^15.1.1"}, "engines": {"node": ">=10.4.0"}}, "node_modules/properties-parser": {"version": "0.3.1", "resolved": "https://registry.npmmirror.com/properties-parser/-/properties-parser-0.3.1.tgz", "integrity": "sha512-AkSQxQAviJ89x4FIxOyHGfO3uund0gvYo7lfD0E+Gp7gFQKrTNgtoYQklu8EhrfHVZUzTwKGZx2r/KDSfnljcA==", "dev": true, "license": "MIT", "dependencies": {"string.prototype.codepointat": "^0.2.0"}, "engines": {"node": ">= 0.3.1"}}, "node_modules/q": {"version": "1.5.1", "resolved": "https://registry.npmmirror.com/q/-/q-1.5.1.tgz", "integrity": "sha512-kV/CThkXo6xyFEZUugw/+pIOywXcDbFYgSct5cT3gqlbkBE1SJdwy6UQoZvodiWF/ckQLZyDE/Bu1M6gVu5lVw==", "deprecated": "You or someone you depend on is using Q, the JavaScript Promise library that gave JavaScript developers strong feelings about promises. They can almost certainly migrate to the native JavaScript promise now. Thank you literally everyone for joining me in this bet against the odds. Be excellent to each other.\n\n(For a CapTP with native promises, see @endo/eventual-send and @endo/captp)", "dev": true, "license": "MIT", "engines": {"node": ">=0.6.0", "teleport": ">=0.2.0"}}, "node_modules/queue-microtask": {"version": "1.2.3", "resolved": "https://registry.npmmirror.com/queue-microtask/-/queue-microtask-1.2.3.tgz", "integrity": "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/read-chunk": {"version": "3.2.0", "resolved": "https://registry.npmmirror.com/read-chunk/-/read-chunk-3.2.0.tgz", "integrity": "sha512-CEjy9LCzhmD7nUpJ1oVOE6s/hBkejlcJEgLQHVnQznOSilOPb+kpKktlLfFDK3/WP43+F80xkUTM2VOkYoSYvQ==", "dev": true, "license": "MIT", "dependencies": {"pify": "^4.0.1", "with-open-file": "^0.1.6"}, "engines": {"node": ">=6"}}, "node_modules/reusify": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/reusify/-/reusify-1.1.0.tgz", "integrity": "sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==", "dev": true, "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/run-parallel": {"version": "1.2.0", "resolved": "https://registry.npmmirror.com/run-parallel/-/run-parallel-1.2.0.tgz", "integrity": "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/sax": {"version": "1.1.4", "resolved": "https://registry.npmmirror.com/sax/-/sax-1.1.4.tgz", "integrity": "sha512-5f3k2PbGGp+YtKJjOItpg3P99IMD84E4HOvcfleTb5joCHNXYLsR9yWFPOYGgaeMPDubQILTCMdsFb2OMeOjtg==", "dev": true, "license": "ISC"}, "node_modules/semver": {"version": "7.7.2", "resolved": "https://registry.npmmirror.com/semver/-/semver-7.7.2.tgz", "integrity": "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/shebang-command": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/shebang-command/-/shebang-command-2.0.0.tgz", "integrity": "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==", "dev": true, "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/shebang-regex/-/shebang-regex-3.0.0.tgz", "integrity": "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/signal-exit": {"version": "3.0.7", "resolved": "https://registry.npmmirror.com/signal-exit/-/signal-exit-3.0.7.tgz", "integrity": "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==", "dev": true, "license": "ISC"}, "node_modules/string-argv": {"version": "0.3.2", "resolved": "https://registry.npmmirror.com/string-argv/-/string-argv-0.3.2.tgz", "integrity": "sha512-aqD2Q0144Z+/RqG52NeHEkZauTAUWJO8c6yTftGJKO3Tja5tUgIfmIl6kExvhtxSDP7fXB6DvzkfMpCd/F3G+Q==", "dev": true, "license": "MIT", "engines": {"node": ">=0.6.19"}}, "node_modules/string.prototype.codepointat": {"version": "0.2.1", "resolved": "https://registry.npmmirror.com/string.prototype.codepointat/-/string.prototype.codepointat-0.2.1.tgz", "integrity": "sha512-2cBVCj6I4IOvEnjgO/hWqXjqBGsY+zwPmHl12Srk9IXSZ56Jwwmy+66XO5Iut/oQVR7t5ihYdLB0GMa4alEUcg==", "dev": true, "license": "MIT"}, "node_modules/strip-bom": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/strip-bom/-/strip-bom-4.0.0.tgz", "integrity": "sha512-3xurFv5tEgii33Zi8Jtp55wEIILR9eh34FAW00PZf+JnSsTmV/ioewSgQl97JHvgjoRGwPShsWm+IdrxB35d0w==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/strip-final-newline": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/strip-final-newline/-/strip-final-newline-2.0.0.tgz", "integrity": "sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/to-regex-range": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==", "dev": true, "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/universalify": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/universalify/-/universalify-2.0.1.tgz", "integrity": "sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/untildify": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/untildify/-/untildify-4.0.0.tgz", "integrity": "sha512-KK8xQ1mkzZeg9inewmFVDNkg3l5LUhoq9kN6iWYB/CC9YMG8HA+c1Q8HwDe6dEX7kErrEVNVBO3fWsVq5iDgtw==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/which": {"version": "3.0.1", "resolved": "https://registry.npmmirror.com/which/-/which-3.0.1.tgz", "integrity": "sha512-XA1b62dzQzLfaEOSQFTCOd5KFf/1VSzZo7/7TUjnya6u0vGGKzU96UQBZTAThCb2j4/xjBAyii1OhRLJEivHvg==", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/which.js"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/with-open-file": {"version": "0.1.7", "resolved": "https://registry.npmmirror.com/with-open-file/-/with-open-file-0.1.7.tgz", "integrity": "sha512-ecJS2/oHtESJ1t3ZfMI3B7KIDKyfN0O16miWxdn30zdh66Yd3LsRFebXZXq6GU4xfxLf6nVxp9kIqElb5fqczA==", "dev": true, "license": "MIT", "dependencies": {"p-finally": "^1.0.0", "p-try": "^2.1.0", "pify": "^4.0.1"}, "engines": {"node": ">=6"}}, "node_modules/xmlbuilder": {"version": "15.1.1", "resolved": "https://registry.npmmirror.com/xmlbuilder/-/xmlbuilder-15.1.1.tgz", "integrity": "sha512-yMqGBqtXyeN1e3TGYvgNgDVZ3j84W4cwkOXQswghol6APgZWaff9lnbvN7MHYJOiXsvGPXtjTYJEiC9J2wv9Eg==", "dev": true, "license": "MIT", "engines": {"node": ">=8.0"}}}}